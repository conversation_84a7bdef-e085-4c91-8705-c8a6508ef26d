"""
训练监控脚本
实时监控CandlestickVQGPT模型的训练过程，检测收敛问题
"""

import os
import time
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from collections import deque
import argparse

def parse_log_file(log_file_path):
    """解析训练日志文件"""
    if not os.path.exists(log_file_path):
        return None
    
    train_losses = []
    val_losses = []
    learning_rates = []
    epochs = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            
            # 解析训练损失
            if 'loss:' in line and 'lr:' in line:
                try:
                    # 提取损失值
                    loss_start = line.find('loss:') + 5
                    loss_end = line.find(',', loss_start)
                    if loss_end == -1:
                        loss_end = line.find(' ', loss_start)
                    loss_val = float(line[loss_start:loss_end].strip())
                    
                    # 提取学习率
                    lr_start = line.find('lr:') + 3
                    lr_val = float(line[lr_start:].strip())
                    
                    train_losses.append(loss_val)
                    learning_rates.append(lr_val)
                except:
                    continue
            
            # 解析验证损失
            if '验证损失:' in line:
                try:
                    val_start = line.find('验证损失:') + 5
                    val_end = line.find(' ', val_start)
                    if val_end == -1:
                        val_end = len(line)
                    val_loss = float(line[val_start:val_end].strip())
                    val_losses.append(val_loss)
                except:
                    continue
            
            # 解析epoch信息
            if 'Epoch' in line and '/' in line:
                try:
                    epoch_start = line.find('Epoch') + 5
                    epoch_end = line.find('/', epoch_start)
                    epoch_num = int(line[epoch_start:epoch_end].strip())
                    epochs.append(epoch_num)
                except:
                    continue
    
    except Exception as e:
        print(f"解析日志文件时出错: {e}")
        return None
    
    return {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'learning_rates': learning_rates,
        'epochs': epochs
    }

def detect_convergence_issues(losses, window_size=50):
    """检测收敛问题"""
    if len(losses) < window_size:
        return []
    
    issues = []
    recent_losses = losses[-window_size:]
    
    # 检测损失是否停止下降
    if len(recent_losses) >= 20:
        early_avg = np.mean(recent_losses[:10])
        late_avg = np.mean(recent_losses[-10:])
        
        if abs(early_avg - late_avg) / early_avg < 0.01:
            issues.append("损失停止下降")
    
    # 检测损失是否震荡
    if len(recent_losses) >= 10:
        loss_std = np.std(recent_losses[-10:])
        loss_mean = np.mean(recent_losses[-10:])
        if loss_std / loss_mean > 0.1:
            issues.append("损失震荡过大")
    
    # 检测损失是否过高
    if len(recent_losses) >= 5:
        recent_avg = np.mean(recent_losses[-5:])
        if recent_avg > 10.0:
            issues.append("损失过高")
    
    # 检测损失是否包含NaN
    if any(np.isnan(loss) or np.isinf(loss) for loss in recent_losses):
        issues.append("损失包含NaN或无穷值")
    
    return issues

def plot_training_progress(data, save_path=None):
    """绘制训练进度图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 训练损失
    if data['train_losses']:
        axes[0, 0].plot(data['train_losses'], label='训练损失', alpha=0.7)
        axes[0, 0].set_title('训练损失')
        axes[0, 0].set_xlabel('步数')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 添加移动平均
        if len(data['train_losses']) > 10:
            window = min(50, len(data['train_losses']) // 10)
            moving_avg = pd.Series(data['train_losses']).rolling(window=window).mean()
            axes[0, 0].plot(moving_avg, label=f'移动平均({window})', color='red', linewidth=2)
            axes[0, 0].legend()
    
    # 验证损失
    if data['val_losses']:
        axes[0, 1].plot(data['val_losses'], label='验证损失', color='orange')
        axes[0, 1].set_title('验证损失')
        axes[0, 1].set_xlabel('评估次数')
        axes[0, 1].set_ylabel('损失')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
    
    # 学习率
    if data['learning_rates']:
        axes[1, 0].plot(data['learning_rates'], label='学习率', color='green')
        axes[1, 0].set_title('学习率变化')
        axes[1, 0].set_xlabel('步数')
        axes[1, 0].set_ylabel('学习率')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        axes[1, 0].set_yscale('log')
    
    # 损失分布
    if data['train_losses']:
        axes[1, 1].hist(data['train_losses'], bins=50, alpha=0.7, label='训练损失分布')
        axes[1, 1].set_title('损失分布')
        axes[1, 1].set_xlabel('损失值')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    plt.show()

def monitor_training(log_file_path, update_interval=30, max_iterations=None):
    """监控训练过程"""
    print(f"开始监控训练日志: {log_file_path}")
    print(f"更新间隔: {update_interval}秒")
    
    iteration = 0
    last_train_loss_count = 0
    
    while True:
        if max_iterations and iteration >= max_iterations:
            break
        
        # 解析日志
        data = parse_log_file(log_file_path)
        
        if data is None:
            print(f"等待日志文件创建: {log_file_path}")
            time.sleep(update_interval)
            iteration += 1
            continue
        
        # 检查是否有新数据
        current_train_loss_count = len(data['train_losses'])
        if current_train_loss_count == last_train_loss_count:
            print(".", end="", flush=True)
            time.sleep(update_interval)
            iteration += 1
            continue
        
        last_train_loss_count = current_train_loss_count
        print(f"\n更新 {iteration}: 发现 {current_train_loss_count} 个训练损失记录")
        
        # 显示最新统计信息
        if data['train_losses']:
            recent_losses = data['train_losses'][-10:]
            print(f"最近10个训练损失: {[f'{loss:.4f}' for loss in recent_losses]}")
            print(f"平均损失: {np.mean(recent_losses):.4f}")
            print(f"损失标准差: {np.std(recent_losses):.4f}")
        
        if data['val_losses']:
            print(f"最新验证损失: {data['val_losses'][-1]:.4f}")
        
        if data['learning_rates']:
            print(f"当前学习率: {data['learning_rates'][-1]:.2e}")
        
        # 检测收敛问题
        issues = detect_convergence_issues(data['train_losses'])
        if issues:
            print(f"⚠️  检测到问题: {', '.join(issues)}")
        
        # 绘制图表
        save_path = os.path.join(os.path.dirname(log_file_path), f'training_progress_{iteration}.png')
        plot_training_progress(data, save_path)
        
        print(f"图表已保存: {save_path}")
        print("-" * 50)
        
        time.sleep(update_interval)
        iteration += 1

def main():
    parser = argparse.ArgumentParser(description='监控CandlestickVQGPT训练过程')
    parser.add_argument('--log_file', type=str, required=True, help='训练日志文件路径')
    parser.add_argument('--update_interval', type=int, default=30, help='更新间隔（秒）')
    parser.add_argument('--max_iterations', type=int, default=None, help='最大监控次数')
    
    args = parser.parse_args()
    
    try:
        monitor_training(args.log_file, args.update_interval, args.max_iterations)
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    main()
