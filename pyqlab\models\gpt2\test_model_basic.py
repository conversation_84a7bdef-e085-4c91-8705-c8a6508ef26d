"""
基础模型测试脚本
快速测试CandlestickVQGPT模型的基本功能
"""

import torch
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from candlestick_vq_gpt import CandlestickVQGPT
from vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod

def test_model_forward():
    """测试模型前向传播"""
    print("=== 测试模型前向传播 ===")

    # 创建模型
    model = CandlestickVQGPT(
        vocab_size=512,
        code_size=100,
        seq_len=30,
        n_layer=4,
        n_head=8,
        d_model=64,
        dropout=0.1,
        use_time_features=True,
        n_time_features=8,
        label_smoothing=0.05,
        use_auxiliary_loss=True
    )

    print(f"模型参数数量: {model.get_num_params():,}")

    # 创建测试输入
    batch_size = 4
    seq_len = 20

    input_tokens = torch.randint(0, 512, (batch_size, seq_len))
    code_ids = torch.randint(0, 100, (batch_size,))
    time_features = torch.randn(batch_size, seq_len, 8)
    targets = torch.randint(0, 512, (batch_size, seq_len))

    print(f"输入形状:")
    print(f"  input_tokens: {input_tokens.shape}")
    print(f"  code_ids: {code_ids.shape}")
    print(f"  time_features: {time_features.shape}")
    print(f"  targets: {targets.shape}")

    # 前向传播
    model.eval()
    with torch.no_grad():
        logits, loss = model(input_tokens, code_ids, time_features, targets)

    print(f"输出形状:")
    print(f"  logits: {logits.shape}")
    print(f"  loss: {loss.item():.4f}")

    # 检查输出多样性
    pred_tokens = logits.argmax(dim=-1)
    unique_predictions = torch.unique(pred_tokens).numel()
    total_predictions = pred_tokens.numel()

    print(f"预测多样性:")
    print(f"  唯一预测数: {unique_predictions}")
    print(f"  总预测数: {total_predictions}")
    print(f"  多样性比例: {unique_predictions / total_predictions:.4f}")

    # 检查logits统计
    logits_flat = logits.view(-1, logits.size(-1))
    logits_mean = logits_flat.mean(dim=-1)
    logits_std = logits_flat.std(dim=-1)

    print(f"Logits统计:")
    print(f"  均值范围: [{logits_mean.min():.4f}, {logits_mean.max():.4f}]")
    print(f"  标准差范围: [{logits_std.min():.4f}, {logits_std.max():.4f}]")
    print(f"  平均标准差: {logits_std.mean():.4f}")

    # 计算预测熵
    probs = F.softmax(logits_flat, dim=-1)
    entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
    print(f"  平均熵: {entropy.mean():.4f}")

    return model

def test_model_generation():
    """测试模型生成功能"""
    print("\n=== 测试模型生成功能 ===")

    # 创建简单模型
    model = CandlestickVQGPT(
        vocab_size=512,
        code_size=100,
        seq_len=30,
        n_layer=2,
        n_head=4,
        d_model=32,
        dropout=0.0,
        use_time_features=False,
        label_smoothing=0.0,
        use_auxiliary_loss=False
    )

    # 创建输入
    input_tokens = torch.randint(0, 512, (1, 10))
    code_ids = torch.randint(0, 100, (1,))

    print(f"输入序列: {input_tokens[0].tolist()}")

    # 生成新token
    model.eval()
    generated = model.generate(
        input_tokens,
        code_ids,
        max_new_tokens=5,
        temperature=1.0,
        top_k=50
    )

    print(f"生成序列: {generated[0].tolist()}")
    print(f"新生成的tokens: {generated[0, 10:].tolist()}")

    # 测试不同温度
    for temp in [0.5, 1.0, 1.5]:
        generated_temp = model.generate(
            input_tokens,
            code_ids,
            max_new_tokens=3,
            temperature=temp,
            top_k=50
        )
        print(f"温度 {temp}: {generated_temp[0, 10:].tolist()}")

def test_tokenizer_integration():
    """测试tokenizer集成"""
    print("\n=== 测试Tokenizer集成 ===")

    try:
        # 创建tokenizer（使用随机码本）
        tokenizer = CandlestickVQTokenizer(
            codebook_weights_path=None,  # 使用随机码本
            num_embeddings=512,
            embedding_dim=4,
            atr_period=14,
            ma_volume_period=14,
            vectorization_method=VectorizationMethod.ATR_BASED,
            detect_gaps=True,
            gap_threshold=2.0
        )

        print(f"Tokenizer词汇表大小: {tokenizer.vocab_size}")
        print(f"特殊token:")
        print(f"  BOS: {tokenizer.token_to_id_map[tokenizer.bos_token]}")
        print(f"  EOS: {tokenizer.token_to_id_map[tokenizer.eos_token]}")
        print(f"  UNK: {tokenizer.unk_token_id}")
        print(f"  GAP_UP: {tokenizer.gap_up_token_id}")
        print(f"  GAP_DOWN: {tokenizer.gap_down_token_id}")

        # 创建模型
        model = CandlestickVQGPT(
            vocab_size=tokenizer.vocab_size,
            code_size=100,
            seq_len=30,
            n_layer=2,
            n_head=4,
            d_model=32,
            dropout=0.0,
            use_time_features=False
        )

        print(f"模型词汇表大小: {model.vocab_size}")
        print("✅ Tokenizer和模型集成成功")

    except Exception as e:
        print(f"❌ Tokenizer集成失败: {e}")

def test_gradient_flow():
    """测试梯度流"""
    print("\n=== 测试梯度流 ===")

    model = CandlestickVQGPT(
        vocab_size=512,
        code_size=100,
        seq_len=30,
        n_layer=2,
        n_head=4,
        d_model=32,
        dropout=0.0,
        use_time_features=False,
        label_smoothing=0.0,
        use_auxiliary_loss=False
    )

    # 创建输入
    input_tokens = torch.randint(0, 512, (2, 10))
    code_ids = torch.randint(0, 100, (2,))
    targets = torch.randint(0, 512, (2, 10))

    # 前向传播
    model.train()
    logits, loss = model(input_tokens, code_ids, targets=targets)

    print(f"损失: {loss.item():.4f}")

    # 反向传播
    loss.backward()

    # 检查梯度
    total_norm = 0
    param_count = 0
    zero_grad_count = 0

    for name, param in model.named_parameters():
        if param.grad is not None:
            param_norm = param.grad.data.norm(2)
            total_norm += param_norm.item() ** 2
            param_count += 1

            if param_norm.item() < 1e-8:
                zero_grad_count += 1
        else:
            print(f"警告: {name} 没有梯度")

    total_norm = total_norm ** (1. / 2)

    print(f"梯度统计:")
    print(f"  总梯度范数: {total_norm:.6f}")
    print(f"  参数数量: {param_count}")
    print(f"  零梯度参数数量: {zero_grad_count}")
    print(f"  零梯度比例: {zero_grad_count / param_count:.4f}")

    if total_norm < 1e-6:
        print("⚠️  警告: 梯度范数过小，可能存在梯度消失问题")
    elif total_norm > 100:
        print("⚠️  警告: 梯度范数过大，可能存在梯度爆炸问题")
    else:
        print("✅ 梯度范数正常")

def main():
    """主测试函数"""
    print("开始基础模型测试...\n")

    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)

    try:
        # 测试前向传播
        model = test_model_forward()

        # 测试生成功能
        test_model_generation()

        # 测试tokenizer集成
        test_tokenizer_integration()

        # 测试梯度流
        test_gradient_flow()

        print("\n=== 测试完成 ===")
        print("✅ 所有基础功能测试通过")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
