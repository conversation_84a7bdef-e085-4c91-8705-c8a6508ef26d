@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt2

REM 改进的训练配置，解决模型预测输出相同值和训练不收敛的问题
REM 主要改进：
REM 1. 增加模型维度 d_model 从 8 -> 64
REM 2. 减少层数 n_layer 从 8 -> 4，避免过深网络难以训练
REM 3. 降低学习率 从 1e-3 -> 5e-4
REM 4. 减少标签平滑 从 0.1 -> 0.05
REM 5. 增加梯度裁剪 从 0 -> 1.0
REM 6. 减少dropout 从 0.1 -> 0.05
REM 7. 增加warmup比例 从 0.1 -> 0.2

python train_candlestick_vq_gpt.py ^
--data_dir f:/hqdata/tsdb ^
--market fut ^
--block_name top ^
--period min1 ^
--begin_date 2025-03-01 ^
--end_date 2025-12-31 ^
--val_ratio 0.1 ^
--stride 1 ^
--codebook_path e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250525\vqcb_atr_based_fut_top_min1_512_0.0209.pt ^
--num_embeddings 512 ^
--embedding_dim 4 ^
--atr_period 14 ^
--ma_volume_period 14 ^
--vectorization_method atr_based ^
--seq_len 30 ^
--code_size 100 ^
--n_layer 4 ^
--n_head 8 ^
--d_model 64 ^
--dropout 0.05 ^
--use_time_features ^
--label_smoothing 0.05 ^
--use_auxiliary_loss ^
--batch_size 32 ^
--epochs 10 ^
--learning_rate 5e-4 ^
--weight_decay 0.01 ^
--warmup_ratio 0.2 ^
--grad_clip 1.0 ^
--grad_accum_steps 4 ^
--early_stopping 5 ^
--num_workers 4 ^
--save_dir e:/lab/RoboQuant/pylab/models/candlestick_vq_gpt_improved ^
--log_interval 50 ^
--eval_interval 200 ^
--save_interval 500 ^
--seed 42 ^
--mixed_precision

pause
