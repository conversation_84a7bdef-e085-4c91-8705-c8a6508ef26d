@echo off
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\gpt2

python backtest_candlestick_vq_gpt.py ^
--data_path f:/hqdata/tsdb/fut_top_min1.parquet ^
--begin_date 2025-04-25 ^
--end_date 2025-12-31 ^
--model_path e:/lab/RoboQuant/pylab/models/candlestick_vq_gpt_improved\vqgpt_atr_based_fut_top_min1_30_4_8_64_518_3.7925_ls.onnx ^
--codebook_path e:\lab\RoboQuant\pylab\models\vqvae\vqvae_20250525\vqcb_atr_based_fut_top_min1_512_0.0209.pt ^
--vectorization_method atr_based ^
--use_time_features ^
--seq_len 30 ^
--initial_capital 100000 ^
--commission 0.001 ^
--threshold 0.6 ^
--stop_loss 0.05 ^
--take_profit 0.1 ^
--temperature 1.5 ^
--top_k 50 ^
--top_p 0.9 ^
--signal_type topk ^
--leverage 10.0 ^
--print_interval 10 ^
--output_dir e:/lab/RoboQuant/pylab/models/results/candlestick_vq_gpt_backtest ^
--seed 42 ^
--use_code_dim ^
--code_dim 5

