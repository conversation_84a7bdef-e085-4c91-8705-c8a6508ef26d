"""
诊断CandlestickVQGPT模型预测多样性的脚本
用于检查模型是否总是预测相同的值，并分析原因
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import argparse
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from candlestick_vq_gpt import CandlestickVQGPT
from vq_tokenizer import CandlestickVQTokenizer, VectorizationMethod
from candlestick_vq_dataset import CandlestickVQDataset
from pyqlab.data.tsdb import TimeSeriesDB

def load_model_and_tokenizer(model_path, args):
    """加载模型和tokenizer"""
    print(f"加载模型: {model_path}")
    
    # 创建tokenizer
    vectorization_method = getattr(VectorizationMethod, args.vectorization_method.upper())
    tokenizer = CandlestickVQTokenizer(
        codebook_weights_path=args.codebook_path,
        num_embeddings=args.num_embeddings,
        embedding_dim=args.embedding_dim,
        atr_period=args.atr_period,
        ma_volume_period=args.ma_volume_period,
        vectorization_method=vectorization_method,
        detect_gaps=True,
        gap_threshold=2.0,
        use_code_dim=False,
        code_size=args.code_size
    )
    
    # 加载模型
    model = CandlestickVQGPT.from_pretrained(model_path)
    model.eval()
    
    return model, tokenizer

def analyze_model_predictions(model, tokenizer, test_data, device, num_samples=100):
    """分析模型预测的多样性"""
    print(f"分析模型预测多样性，使用 {num_samples} 个样本...")
    
    model.to(device)
    predictions = []
    logits_stats = []
    
    with torch.no_grad():
        for i in range(min(num_samples, len(test_data))):
            sample = test_data[i]
            
            # 准备输入
            input_tokens = sample['input_tokens'].unsqueeze(0).to(device)
            code_ids = sample['code_ids'].unsqueeze(0).to(device)
            time_features = sample.get('time_features')
            if time_features is not None:
                time_features = time_features.unsqueeze(0).to(device)
            
            # 前向传播
            logits, _ = model(input_tokens, code_ids, time_features)
            
            # 获取最后一个时间步的预测
            last_logits = logits[0, -1, :]  # [vocab_size]
            
            # 统计logits
            logits_stats.append({
                'mean': last_logits.mean().item(),
                'std': last_logits.std().item(),
                'min': last_logits.min().item(),
                'max': last_logits.max().item(),
                'entropy': -torch.sum(F.softmax(last_logits, dim=-1) * F.log_softmax(last_logits, dim=-1)).item()
            })
            
            # 获取预测
            pred_token = last_logits.argmax().item()
            predictions.append(pred_token)
            
            # 获取top-k预测
            top_k_values, top_k_indices = torch.topk(last_logits, k=5)
            
            if i < 10:  # 打印前10个样本的详细信息
                print(f"样本 {i}:")
                print(f"  输入tokens: {input_tokens[0].cpu().numpy()}")
                print(f"  预测token: {pred_token}")
                print(f"  Top-5预测: {top_k_indices.cpu().numpy()}")
                print(f"  Top-5概率: {F.softmax(top_k_values, dim=-1).cpu().numpy()}")
                print(f"  Logits统计: mean={logits_stats[-1]['mean']:.4f}, std={logits_stats[-1]['std']:.4f}")
                print(f"  熵: {logits_stats[-1]['entropy']:.4f}")
                print()
    
    return predictions, logits_stats

def plot_prediction_analysis(predictions, logits_stats, save_dir):
    """绘制预测分析图表"""
    print("绘制分析图表...")
    
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. 预测分布直方图
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 3, 1)
    pred_counts = Counter(predictions)
    plt.bar(pred_counts.keys(), pred_counts.values())
    plt.title(f'预测Token分布 (唯一值: {len(pred_counts)})')
    plt.xlabel('Token ID')
    plt.ylabel('频次')
    
    # 2. Logits统计
    logits_df = pd.DataFrame(logits_stats)
    
    plt.subplot(2, 3, 2)
    plt.hist(logits_df['mean'], bins=20, alpha=0.7)
    plt.title('Logits均值分布')
    plt.xlabel('均值')
    plt.ylabel('频次')
    
    plt.subplot(2, 3, 3)
    plt.hist(logits_df['std'], bins=20, alpha=0.7)
    plt.title('Logits标准差分布')
    plt.xlabel('标准差')
    plt.ylabel('频次')
    
    plt.subplot(2, 3, 4)
    plt.hist(logits_df['entropy'], bins=20, alpha=0.7)
    plt.title('预测熵分布')
    plt.xlabel('熵')
    plt.ylabel('频次')
    
    plt.subplot(2, 3, 5)
    plt.scatter(logits_df['std'], logits_df['entropy'], alpha=0.6)
    plt.title('标准差 vs 熵')
    plt.xlabel('标准差')
    plt.ylabel('熵')
    
    plt.subplot(2, 3, 6)
    plt.plot(logits_df['entropy'])
    plt.title('熵随样本变化')
    plt.xlabel('样本索引')
    plt.ylabel('熵')
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'prediction_analysis.png'), dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印统计信息
    print("\n=== 预测多样性分析 ===")
    print(f"总样本数: {len(predictions)}")
    print(f"唯一预测数: {len(set(predictions))}")
    print(f"预测多样性比例: {len(set(predictions)) / len(predictions):.4f}")
    print(f"最频繁的预测: {max(pred_counts, key=pred_counts.get)} (出现 {pred_counts[max(pred_counts, key=pred_counts.get)]} 次)")
    
    print(f"\n=== Logits统计 ===")
    print(f"平均熵: {logits_df['entropy'].mean():.4f} ± {logits_df['entropy'].std():.4f}")
    print(f"平均标准差: {logits_df['std'].mean():.4f} ± {logits_df['std'].std():.4f}")
    print(f"平均均值: {logits_df['mean'].mean():.4f} ± {logits_df['mean'].std():.4f}")
    
    # 判断是否存在问题
    if len(set(predictions)) < len(predictions) * 0.1:
        print("\n⚠️  警告: 预测多样性过低，模型可能存在问题！")
    if logits_df['entropy'].mean() < 1.0:
        print("⚠️  警告: 预测熵过低，模型过于确定！")
    if logits_df['std'].mean() < 0.1:
        print("⚠️  警告: Logits标准差过低，模型输出过于平坦！")

def main():
    parser = argparse.ArgumentParser(description='诊断CandlestickVQGPT模型预测多样性')
    
    # 模型参数
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--codebook_path', type=str, required=True, help='码本路径')
    parser.add_argument('--data_dir', type=str, required=True, help='数据目录')
    parser.add_argument('--market', type=str, default='fut', help='市场类型')
    parser.add_argument('--block_name', type=str, default='top', help='板块名称')
    parser.add_argument('--period', type=str, default='min1', help='时间周期')
    parser.add_argument('--begin_date', type=str, default='2025-03-01', help='开始日期')
    parser.add_argument('--end_date', type=str, default='2025-12-31', help='结束日期')
    
    # Tokenizer参数
    parser.add_argument('--num_embeddings', type=int, default=512, help='码本大小')
    parser.add_argument('--embedding_dim', type=int, default=4, help='嵌入维度')
    parser.add_argument('--atr_period', type=int, default=14, help='ATR周期')
    parser.add_argument('--ma_volume_period', type=int, default=14, help='成交量移动平均周期')
    parser.add_argument('--vectorization_method', type=str, default='atr_based', help='向量化方法')
    parser.add_argument('--seq_len', type=int, default=30, help='序列长度')
    parser.add_argument('--code_size', type=int, default=100, help='证券代码数量')
    
    # 分析参数
    parser.add_argument('--num_samples', type=int, default=100, help='分析样本数量')
    parser.add_argument('--save_dir', type=str, default='./diagnosis_results', help='结果保存目录')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型和tokenizer
    model, tokenizer = load_model_and_tokenizer(args.model_path, args)
    
    # 加载测试数据
    print("加载测试数据...")
    tsdb = TimeSeriesDB(args.data_dir)
    data, code_ids = tsdb.load_market_data(
        market=args.market,
        block_name=args.block_name,
        period=args.period,
        begin_date=args.begin_date,
        end_date=args.end_date
    )
    
    # 创建数据集
    dataset = CandlestickVQDataset(
        data=data,
        code_ids=code_ids,
        tokenizer=tokenizer,
        seq_len=args.seq_len,
        stride=1,
        use_time_features=True
    )
    
    # 分析预测
    predictions, logits_stats = analyze_model_predictions(
        model, tokenizer, dataset, device, args.num_samples
    )
    
    # 绘制分析图表
    plot_prediction_analysis(predictions, logits_stats, args.save_dir)

if __name__ == "__main__":
    main()
